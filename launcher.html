<!DOCTYPE html>
<html>
<head>
    <title>Temperature Color Rotation - Launcher</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 50px;
            background-color: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .loading { background-color: #fff3cd; color: #856404; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button {
            padding: 10px 20px;
            font-size: 16px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .primary { background-color: #007bff; color: white; }
        .secondary { background-color: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌡️ Temperature Color Rotation</h1>
        <p>This launcher will find your Flask server and open the application.</p>
        
        <div id="status" class="status loading">
            🔍 Searching for Flask server...
        </div>
        
        <button class="primary" onclick="findServer()">🔍 Find Server</button>
        <button class="secondary" onclick="openManual()">📝 Manual URLs</button>
        
        <div id="manual" style="display:none; margin-top:20px;">
            <h3>Try these URLs manually:</h3>
            <p><a href="http://127.0.0.1:5000" target="_blank">http://127.0.0.1:5000</a></p>
            <p><a href="http://127.0.0.1:5001" target="_blank">http://127.0.0.1:5001</a></p>
            <p><a href="http://127.0.0.1:5002" target="_blank">http://127.0.0.1:5002</a></p>
            <p><a href="http://127.0.0.1:5003" target="_blank">http://127.0.0.1:5003</a></p>
            <p><a href="http://127.0.0.1:5004" target="_blank">http://127.0.0.1:5004</a></p>
        </div>
    </div>

    <script>
        async function checkPort(port) {
            try {
                const response = await fetch(`http://127.0.0.1:${port}/get_temperatures`, {
                    method: 'GET',
                    timeout: 2000
                });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        async function findServer() {
            const status = document.getElementById('status');
            status.className = 'status loading';
            status.innerHTML = '🔍 Searching for Flask server...';

            for (let port = 5000; port <= 5009; port++) {
                status.innerHTML = `🔍 Checking port ${port}...`;
                
                if (await checkPort(port)) {
                    status.className = 'status success';
                    status.innerHTML = `✅ Found server on port ${port}! Opening application...`;
                    
                    setTimeout(() => {
                        window.open(`http://127.0.0.1:${port}`, '_blank');
                    }, 1000);
                    return;
                }
            }

            status.className = 'status error';
            status.innerHTML = '❌ No Flask server found. Please start the server first using: ./start_server.sh';
        }

        function openManual() {
            document.getElementById('manual').style.display = 'block';
        }

        // Auto-search on page load
        window.onload = function() {
            setTimeout(findServer, 500);
        };
    </script>
</body>
</html>

from flask import Flask, render_template, jsonify
import json

app = Flask(__name__)

def get_color_for_temperature(temperature):
    """Get color based on temperature range"""
    if temperature < 10:
        return "#00FFFF"  # <PERSON>an (Cold)
    elif 10 <= temperature < 20:
        return "#FFFF00"  # Yellow (Mild)
    elif 20 <= temperature <= 30:
        return "#FF0000"  # Red (Hot)
    else:
        return "#808080"  # Gray (Out of range)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/view_transactions')
def view_transactions():
    """View current transactions data"""
    try:
        with open('../transactions.json', 'r') as f:
            data = json.load(f)
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/get_temperatures')
def get_temperatures():
    """Get all temperatures from transactions for rotation"""
    try:
        with open('../transactions.json', 'r') as f:
            data = json.load(f)

        temperatures = []
        for tx in data['transactions']:
            if 'temperature' in tx:
                temperatures.append({
                    'temperature': tx['temperature'],
                    'color': get_color_for_temperature(tx['temperature']),
                    'txid': tx['txid'][:16] + '...',
                    'timestamp': tx.get('timestamp', ''),
                    'op_return_data': tx.get('op_return_data', '')
                })

        return jsonify({'temperatures': temperatures})
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    import socket

    # Try to find an available port starting from 5000
    for port in range(5000, 5010):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('127.0.0.1', port))
            sock.close()
            print(f"Starting server on port {port}")
            app.run(debug=True, port=port, host='127.0.0.1')
            break
        except OSError:
            continue
    else:
        print("No available ports found in range 5000-5009")

#  ./start_server.sh (use "running on" port) or launcher.html, or python3 venv/app.py
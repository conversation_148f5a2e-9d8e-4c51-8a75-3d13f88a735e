from flask import Flask, render_template, jsonify
import json

app = Flask(__name__)

def get_color_for_number(number):
    """Get color based on number range"""
    if 0 <= number <= 9:
        return "#00FFFF"  # <PERSON>an
    elif 10 <= number <= 19:
        return "#FFFF00"  # Yellow
    elif 20 <= number <= 30:
        return "#FF0000"  # Red
    else:
        return "#808080"  # Gray

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/view_transactions')
def view_transactions():
    """View current transactions data"""
    try:
        with open('transactions.json', 'r') as f:
            data = json.load(f)
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/get_numbers')
def get_numbers():
    """Get all numbers from transactions for rotation"""
    try:
        with open('transactions.json', 'r') as f:
            data = json.load(f)
        
        numbers = []
        for tx in data['transactions']:
            if 'number' in tx:
                numbers.append({
                    'number': tx['number'],
                    'color': get_color_for_number(tx['number']),
                    'txid': tx['txid'][:16] + '...'
                })
        
        return jsonify({'numbers': numbers})
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    app.run(debug=True, port=5001)

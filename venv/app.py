from flask import Flask, render_template, request, jsonify
import serial
import time
import asyncio
import requests
import json
from bsv import PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput, OpReturn, WhatsOnChainBroadcaster

app = Flask(__name__)

# BSV configuration
PRIVATE_KEY = 'your_private_key_here'
CURRENT_UTXO = {
    'txid': 'your_utxo_txid_here',
    'hex': 'your_utxo_hex_here',
    'output_index': 0
}

def get_temperature():
    try:
        arduino = serial.Serial('/dev/ttyACM0', 9600, timeout=2)
        time.sleep(2)
        
        arduino.write(b'READ_TEMP\n')
        time.sleep(1)
        
        temp_data = arduino.readline().decode().strip()
        print(f"Raw data received: '{temp_data}'")
        arduino.close()
        
        if temp_data and temp_data.startswith('TEMP:'):
            temp_str = temp_data.replace('TEMP:', '').strip()
            temp_value = float(temp_str)
            print(f"Parsed temperature: {temp_value}")
            return temp_value
        return 0.0
    except Exception as e:
        print(f"Error: {e}")
        return 22.5

def get_color_for_number(number):
    """Get color based on number range"""
    if 0 <= number <= 9:
        return "#00FFFF"  # Cyan
    elif 10 <= number <= 19:
        return "#FFFF00"  # Yellow
    elif 20 <= number <= 30:
        return "#FF0000"  # Red
    else:
        return "#808080"  # Gray

def get_op_return_data(txid):
    """Get OP_RETURN data from a transaction"""
    try:
        url = f"https://api.whatsonchain.com/v1/bsv/main/tx/{txid}"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            tx_data = response.json()
            
            for output in tx_data.get('vout', []):
                script_pub_key = output.get('scriptPubKey', {})
                if script_pub_key.get('type') == 'nulldata':
                    hex_data = script_pub_key.get('hex', '')
                    if hex_data.startswith('6a'):
                        data_hex = hex_data[4:]
                        try:
                            decoded = bytes.fromhex(data_hex).decode('utf-8')
                            return decoded
                        except:
                            return hex_data
            return None
        return None
    except Exception as e:
        print(f"Error fetching OP_RETURN for {txid}: {e}")
        return None

def update_transactions_file():
    """Update transactions.json with OP_RETURN data"""
    try:
        with open('venv/transactions.json', 'r') as f:
            data = json.load(f)
        
        for tx in data['transactions']:
            if not tx.get('op_return_data'):
                op_return = get_op_return_data(tx['txid'])
                if op_return:
                    tx['op_return_data'] = op_return
                    if op_return.startswith('number:'):
                        try:
                            number = float(op_return.replace('number:', ''))
                            tx['number'] = number
                        except:
                            pass
                print(f"Updated {tx['txid']}: {op_return}")
        
        with open('venv/transactions.json', 'w') as f:
            json.dump(data, f, indent=2)
        
        return data
    except Exception as e:
        print(f"Error updating transactions: {e}")
        return None

async def store_number_on_bsv(number):
    """Store a number on BSV blockchain"""
    try:
        priv_key = PrivateKey(PRIVATE_KEY)
        source_tx = Transaction.from_hex(CURRENT_UTXO['hex'])

        tx_input = TransactionInput(
            source_transaction=source_tx,
            source_txid=CURRENT_UTXO['txid'],
            source_output_index=CURRENT_UTXO['output_index'],
            unlocking_script_template=P2PKH().unlock(priv_key),
        )

        op_return_output = TransactionOutput(
            locking_script=OpReturn().lock([f"number:{number}"]),
            satoshis=0
        )

        change_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            change=True
        )

        tx = Transaction([tx_input], [change_output, op_return_output], version=1)
        tx.fee(250)
        tx.sign()

        broadcaster = WhatsOnChainBroadcaster()
        await broadcaster.broadcast(tx)
        
        return tx.txid()
    except Exception as e:
        print(f"Blockchain error: {e}")
        raise

@app.route('/')
def index():
    temperature = get_temperature()
    return render_template('index.html', temp=temperature)

@app.route('/store_number', methods=['POST'])
def store_number():
    try:
        number = float(request.json['number'])
        
        if not (0 < number < 30):
            return jsonify({'success': False, 'message': 'Number must be between 0 and 30'})
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        txid = loop.run_until_complete(store_number_on_bsv(number))
        loop.close()
        
        return jsonify({
            'success': True, 
            'txid': txid,
            'number': number,
            'url': f'https://whatsonchain.com/tx/{txid}'
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/update_transactions')
def update_transactions():
    """Update transactions.json with OP_RETURN data"""
    data = update_transactions_file()
    if data:
        return jsonify({'success': True, 'transactions': data['transactions']})
    else:
        return jsonify({'success': False, 'message': 'Failed to update transactions'})

@app.route('/view_transactions')
def view_transactions():
    """View current transactions data"""
    try:
        with open('venv/transactions.json', 'r') as f:
            data = json.load(f)
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/get_numbers')
def get_numbers():
    """Get all numbers from transactions for rotation"""
    try:
        with open('venv/transactions.json', 'r') as f:
            data = json.load(f)
        
        numbers = []
        for tx in data['transactions']:
            if 'number' in tx:
                numbers.append({
                    'number': tx['number'],
                    'color': get_color_for_number(tx['number']),
                    'txid': tx['txid'][:16] + '...'
                })
        
        return jsonify({'numbers': numbers})
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    app.run(debug=True)

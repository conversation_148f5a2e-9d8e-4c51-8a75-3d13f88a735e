
<!DOCTYPE html>
<html>
<head>
    <title>Color Rotation</title>
    <style>
        .color-box {
            width: 200px;
            height: 200px;
            border: 3px solid #333;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            transition: all 0.5s ease;
        }
        .controls {
            text-align: center;
            margin: 20px;
        }
        button {
            margin: 5px;
            padding: 10px 20px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <h1>Temperature Color Rotation from Blockchain</h1>
    <div class="color-box" id="colorBox" style="background-color: #808080;">
        Ready
    </div>
    
    <div class="controls">
        <button onclick="startRotation()">Start Rotation</button>
        <button onclick="stopRotation()">Stop Rotation</button>
        <button onclick="testFunction()">Test (Debug)</button>
        <div id="rotationInfo">Click "Start Rotation" to begin</div>
    </div>
    
    <hr>

    <h3>Transaction Data</h3>
    <button onclick="viewTransactions()">View Transactions</button>

    <div id="transactions"></div>

    <script>
        let rotationInterval = null;
        let currentIndex = 0;
        let temperatures = [];

        function loadTemperatures() {
            console.log('Loading temperatures from API...');
            return fetch('/get_temperatures')
            .then(response => {
                console.log('API response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('API response data:', data);
                if (data.temperatures) {
                    temperatures = data.temperatures;
                    console.log('Loaded temperatures:', temperatures);
                    return temperatures;
                } else {
                    console.error('No temperatures found in response');
                    return [];
                }
            })
            .catch(error => {
                console.error('Error loading temperatures:', error);
                return [];
            });
        }

        function startRotation() {
            console.log('Start rotation clicked, temperatures array:', temperatures);
            if (temperatures.length === 0) {
                console.log('Temperatures array is empty, loading temperatures...');
                loadTemperatures().then(() => {
                    if (temperatures.length > 0) {
                        startRotation();
                    } else {
                        console.error('Failed to load temperatures');
                    }
                });
                return;
            }

            if (rotationInterval) {
                clearInterval(rotationInterval);
            }

            const colorBox = document.getElementById('colorBox');
            const rotationInfo = document.getElementById('rotationInfo');
            
            rotationInterval = setInterval(() => {
                if (temperatures.length > 0) {
                    const current = temperatures[currentIndex];

                    colorBox.style.backgroundColor = current.color;
                    colorBox.textContent = current.temperature + '°C';

                    rotationInfo.innerHTML = `Temperature: ${current.temperature}°C | TxID: ${current.txid}`;

                    currentIndex = (currentIndex + 1) % temperatures.length;
                }
            }, 2000); // Change every 2 seconds
            
            rotationInfo.textContent = 'Rotation started...';
        }

        function stopRotation() {
            if (rotationInterval) {
                clearInterval(rotationInterval);
                rotationInterval = null;
                document.getElementById('rotationInfo').textContent = 'Rotation stopped';
            }
        }

        function testFunction() {
            console.log('Test button clicked!');
            console.log('Temperatures array:', temperatures);
            console.log('Temperatures length:', temperatures.length);
            alert('Test button works! Check console for details.');
        }

        function viewTransactions() {
            fetch('/view_transactions')
            .then(response => response.json())
            .then(data => {
                const div = document.getElementById('transactions');
                let html = '<h4>Stored Transactions:</h4>';

                data.transactions.forEach(tx => {
                    html += `<p><strong>TxID:</strong> ${tx.txid.substring(0, 16)}...<br>
                             <strong>Temperature:</strong> ${tx.temperature}°C<br>
                             <strong>Timestamp:</strong> ${tx.timestamp}<br>
                             <strong>OP_RETURN:</strong> ${tx.op_return_data}</p><hr>`;
                });

                div.innerHTML = html;
            });
        }

        // Load temperatures when page loads
        window.onload = function() {
            loadTemperatures();
        };
    </script>
</body>
</html>
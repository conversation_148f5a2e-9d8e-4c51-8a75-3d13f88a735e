
<!DOCTYPE html>
<html>
<head>
    <title>Color Rotation</title>
    <style>
        .color-box {
            width: 200px;
            height: 200px;
            border: 3px solid #333;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            transition: all 0.5s ease;
        }
        .controls {
            text-align: center;
            margin: 20px;
        }
        button {
            margin: 5px;
            padding: 10px 20px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <h1>Color Rotation from Transactions</h1>
    <div class="color-box" id="colorBox" style="background-color: #808080;">
        Ready
    </div>
    
    <div class="controls">
        <button onclick="startRotation()">Start Rotation</button>
        <button onclick="stopRotation()">Stop Rotation</button>
        <div id="rotationInfo">Click "Start Rotation" to begin</div>
    </div>
    
    <hr>

    <h3>Transaction Data</h3>
    <button onclick="viewTransactions()">View Transactions</button>

    <div id="transactions"></div>

    <script>
        let rotationInterval = null;
        let currentIndex = 0;
        let numbers = [];

        function loadNumbers() {
            fetch('/get_numbers')
            .then(response => response.json())
            .then(data => {
                if (data.numbers) {
                    numbers = data.numbers;
                    console.log('Loaded numbers:', numbers);
                } else {
                    console.error('No numbers found');
                }
            })
            .catch(error => {
                console.error('Error loading numbers:', error);
            });
        }

        function startRotation() {
            if (numbers.length === 0) {
                loadNumbers();
                setTimeout(startRotation, 1000); // Try again after loading
                return;
            }

            if (rotationInterval) {
                clearInterval(rotationInterval);
            }

            const colorBox = document.getElementById('colorBox');
            const rotationInfo = document.getElementById('rotationInfo');
            
            rotationInterval = setInterval(() => {
                if (numbers.length > 0) {
                    const current = numbers[currentIndex];
                    
                    colorBox.style.backgroundColor = current.color;
                    colorBox.textContent = current.number;
                    
                    rotationInfo.innerHTML = `Number: ${current.number} | TxID: ${current.txid}`;
                    
                    currentIndex = (currentIndex + 1) % numbers.length;
                }
            }, 2000); // Change every 2 seconds
            
            rotationInfo.textContent = 'Rotation started...';
        }

        function stopRotation() {
            if (rotationInterval) {
                clearInterval(rotationInterval);
                rotationInterval = null;
                document.getElementById('rotationInfo').textContent = 'Rotation stopped';
            }
        }

        function viewTransactions() {
            fetch('/view_transactions')
            .then(response => response.json())
            .then(data => {
                const div = document.getElementById('transactions');
                let html = '<h4>Stored Transactions:</h4>';
                
                data.transactions.forEach(tx => {
                    html += `<p><strong>TxID:</strong> ${tx.txid.substring(0, 16)}...<br>
                             <strong>Number:</strong> ${tx.number}<br>
                             <strong>OP_RETURN:</strong> ${tx.op_return_data}</p><hr>`;
                });
                
                div.innerHTML = html;
            });
        }

        // Load numbers when page loads
        window.onload = function() {
            loadNumbers();
        };
    </script>
</body>
</html>